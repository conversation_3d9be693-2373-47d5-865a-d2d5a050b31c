/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e293b;
    line-height: 1.6;
    min-height: 100vh;
}

.app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Color Variables */
:root {
    --primary-color: #5e2e60;
    --secondary-color: #5f8132;
    --tertiary-color: #327881;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --light-bg: #f8fafc;
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #7c3aed 100%);
    color: white;
    padding: 2rem 0;
    box-shadow: var(--shadow-lg);
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.header h1 i {
    color: #10b981;
}

.header p {
    font-size: 1.125rem;
    opacity: 0.9;
    font-weight: 400;
}

/* Main Content */
.main {
    flex: 1;
    padding: 3rem 0;
}

/* Form Sections */
.form-section {
    background: var(--white);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.form-section h2 {
    color: var(--gray-800);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-100);
}

.form-section h2 i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

/* Form Grid */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.875rem 1rem;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--white);
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 46, 96, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Price Toggle */
.price-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

.toggle-switch input:checked + .slider {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + .slider:before {
    transform: translateX(26px);
}

.price-toggle span {
    font-weight: 500;
    color: var(--gray-700);
}

/* Grave Items */
.grave-item {
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.2s ease;
}

.grave-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.grave-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.grave-header h3 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
}

.btn-remove {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.btn-remove:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.price-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.price-display .price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

.price-display small {
    color: var(--gray-600);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Buttons */
.btn-add {
    background: linear-gradient(135deg, var(--secondary-color), #10b981);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-generate {
    background: linear-gradient(135deg, var(--primary-color), #7c3aed);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1.25rem 2rem;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    margin-top: 1rem;
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-generate:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Additional Services */
.additional-services {
    display: grid;
    gap: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 4px;
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.service-name {
    flex: 1;
    font-weight: 500;
    color: var(--gray-700);
}

.service-price {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1rem;
}

/* Summary Section */
.summary-section {
    background: linear-gradient(135deg, var(--gray-50), var(--white));
    border: 2px solid var(--gray-200);
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    text-align: center;
}

.total-price {
    margin-bottom: 2rem;
}

.total-price h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.total-price p {
    color: var(--gray-600);
    font-size: 1rem;
    margin-bottom: 1rem;
}

.price-breakdown {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.price-breakdown div {
    flex: 1;
    text-align: center;
}

/* Footer */
.footer {
    background: var(--gray-800);
    color: var(--gray-300);
    padding: 2rem 0;
    text-align: center;
    margin-top: auto;
}

.footer p {
    margin: 0;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header h1 {
        font-size: 2rem;
    }

    .header p {
        font-size: 1rem;
    }

    .form-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .container {
        padding: 0 0.75rem;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 1.5rem 0;
    }

    .header h1 {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .header p {
        font-size: 0.875rem;
    }

    .main {
        padding: 2rem 0;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .grave-item {
        padding: 1rem;
    }

    .grave-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .price-breakdown {
        flex-direction: column;
        gap: 0.5rem;
    }

    .total-price h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .header p {
        font-size: 0.8rem;
    }

    .form-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .form-section h2 {
        font-size: 1.25rem;
    }

    .btn-generate {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .total-price h2 {
        font-size: 1.25rem;
    }

    .container {
        padding: 0 0.5rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.hidden { display: none !important; }
.visible { display: block !important; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
